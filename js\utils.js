
// Utility functions for performance and common operations

// Configuration for Google Custom Search API
const CONFIG = {
    GOOGLE_API_KEY: 'AIzaSyC3ZD5RiNGkyUPjOspKMN5HlPe2AqSUPvM', // Your API key
    SEARCH_ENGINE_ID: '30a8567a4e17d49d2', // Your Search Engine ID
    BASE_URL: 'https://www.googleapis.com/customsearch/v1',
    RESULTS_PER_PAGE: 10,
    MAX_CACHE_SIZE: 50,
    DEBOUNCE_DELAY: 300
};

// Cache for storing search results and suggestions
class Cache {
    constructor(maxSize = CONFIG.MAX_CACHE_SIZE) {
        this.cache = new Map();
        this.maxSize = maxSize;
    }
    
    get(key) {
        if (this.cache.has(key)) {
            // Move to end (most recently used)
            const value = this.cache.get(key);
            this.cache.delete(key);
            this.cache.set(key, value);
            return value;
        }
        return null;
    }
    
    set(key, value) {
        if (this.cache.has(key)) {
            this.cache.delete(key);
        } else if (this.cache.size >= this.maxSize) {
            // Remove least recently used
            const firstKey = this.cache.keys().next().value;
            this.cache.delete(firstKey);
        }
        this.cache.set(key, value);
    }
    
    clear() {
        this.cache.clear();
    }
}

// Global cache instances
const searchCache = new Cache();
const suggestionCache = new Cache();

// Debounce function for performance optimization
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// Throttle function for scroll events
function throttle(func, limit) {
    let inThrottle;
    return function() {
        const args = arguments;
        const context = this;
        if (!inThrottle) {
            func.apply(context, args);
            inThrottle = true;
            setTimeout(() => inThrottle = false, limit);
        }
    };
}

// URL parameter utilities
function getUrlParams() {
    const params = new URLSearchParams(window.location.search);
    return {
        q: params.get('q') || '',
        start: parseInt(params.get('start')) || 1,
        type: params.get('type') || 'web'
    };
}

function updateUrlParams(params) {
    const url = new URL(window.location);
    Object.keys(params).forEach(key => {
        if (params[key]) {
            url.searchParams.set(key, params[key]);
        } else {
            url.searchParams.delete(key);
        }
    });
    window.history.pushState({}, '', url);
}

// Local storage utilities
function saveToStorage(key, data) {
    try {
        localStorage.setItem(key, JSON.stringify(data));
    } catch (e) {
        console.warn('Failed to save to localStorage:', e);
    }
}

function getFromStorage(key) {
    try {
        const data = localStorage.getItem(key);
        return data ? JSON.parse(data) : null;
    } catch (e) {
        console.warn('Failed to read from localStorage:', e);
        return null;
    }
}

// Format numbers with commas
function formatNumber(num) {
    return num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',');
}

// Escape HTML to prevent XSS
function escapeHtml(text) {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
}

// Extract domain from URL
function extractDomain(url) {
    try {
        return new URL(url).hostname.replace('www.', '');
    } catch (e) {
        return url;
    }
}

// Format time ago
function timeAgo(date) {
    const now = new Date();
    const diffInSeconds = Math.floor((now - new Date(date)) / 1000);
    
    if (diffInSeconds < 60) return 'Just now';
    if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)} minutes ago`;
    if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)} hours ago`;
    if (diffInSeconds < 2592000) return `${Math.floor(diffInSeconds / 86400)} days ago`;
    
    return new Date(date).toLocaleDateString();
}

// Performance monitoring
function measurePerformance(name, fn) {
    const start = performance.now();
    const result = fn();
    const end = performance.now();
    console.log(`${name} took ${end - start} milliseconds`);
    return result;
}

// Error handling
function handleError(error, context = '') {
    console.error(`Error in ${context}:`, error);
    
    // Show user-friendly error message
    const errorDiv = document.createElement('div');
    errorDiv.className = 'error-message';
    errorDiv.innerHTML = `
        <strong>Oops! Something went wrong.</strong><br>
        ${context ? `Error in ${context}. ` : ''}
        Please try again in a moment.
    `;
    
    return errorDiv;
}

// Check if API keys are configured
function checkApiConfiguration() {
    // For demo purposes, always return false to show demo results
    // Change this to true when you have valid API credentials
    const DEMO_MODE = true;

    if (DEMO_MODE) {
        console.log('Running in demo mode - showing sample results');
        return false;
    }

    if (CONFIG.GOOGLE_API_KEY === 'YOUR_API_KEY_HERE' ||
        CONFIG.SEARCH_ENGINE_ID === 'YOUR_SEARCH_ENGINE_ID_HERE' ||
        !CONFIG.GOOGLE_API_KEY ||
        !CONFIG.SEARCH_ENGINE_ID ||
        CONFIG.GOOGLE_API_KEY.length < 10 ||
        CONFIG.SEARCH_ENGINE_ID.length < 10) {
        console.warn('Google Custom Search API not configured properly. Please set your API key and Search Engine ID in js/utils.js');
        return false;
    }
    return true;
}

// Lazy loading for images
function setupLazyLoading() {
    if ('IntersectionObserver' in window) {
        const imageObserver = new IntersectionObserver((entries, observer) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const img = entry.target;
                    img.src = img.dataset.src;
                    img.classList.remove('lazy');
                    imageObserver.unobserve(img);
                }
            });
        });
        
        document.querySelectorAll('img[data-src]').forEach(img => {
            imageObserver.observe(img);
        });
    }
}

// Initialize performance optimizations
document.addEventListener('DOMContentLoaded', () => {
    // Setup lazy loading
    setupLazyLoading();
    
    // Preload critical resources
    const criticalResources = [
        'styles/main.css',
        'js/search.js',
        'js/autocomplete.js'
    ];
    
    criticalResources.forEach(resource => {
        const link = document.createElement('link');
        link.rel = 'preload';
        link.href = resource;
        link.as = resource.endsWith('.css') ? 'style' : 'script';
        document.head.appendChild(link);
    });
});

// Export for use in other modules
window.Utils = {
    CONFIG,
    Cache,
    searchCache,
    suggestionCache,
    debounce,
    throttle,
    getUrlParams,
    updateUrlParams,
    saveToStorage,
    getFromStorage,
    formatNumber,
    escapeHtml,
    extractDomain,
    timeAgo,
    measurePerformance,
    handleError,
    checkApiConfiguration,
    setupLazyLoading
};

