<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title id="page-title">Search Results</title>
    <meta name="description" content="Search results page">
    
    <link rel="preload" href="styles/main.css" as="style">
    <link rel="preload" href="js/results.js" as="script">
    
    <!-- Critical CSS for results page -->
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #fff;
            color: #202124;
            line-height: 1.4;
        }
        
        .header {
            padding: 20px;
            border-bottom: 1px solid #dadce0;
            background: white;
            position: sticky;
            top: 0;
            z-index: 100;
        }
        
        .header-content {
            max-width: 1200px;
            margin: 0 auto;
            display: flex;
            align-items: center;
            gap: 30px;
        }
        
        .logo {
            font-size: 24px;
            font-weight: 400;
            color: #4285f4;
            text-decoration: none;
        }
        
        .search-container {
            flex: 1;
            max-width: 500px;
            position: relative;
        }
        
        .search-box {
            width: 100%;
            height: 40px;
            border: 1px solid #dfe1e5;
            border-radius: 20px;
            padding: 0 40px 0 16px;
            font-size: 16px;
            outline: none;
        }
        
        .search-icon {
            position: absolute;
            right: 12px;
            top: 50%;
            transform: translateY(-50%);
            width: 16px;
            height: 16px;
            cursor: pointer;
        }
        
        .nav-tabs {
            padding: 0 20px;
            background: white;
            border-bottom: 1px solid #dadce0;
        }
        
        .nav-content {
            max-width: 1200px;
            margin: 0 auto;
            display: flex;
            gap: 30px;
        }
        
        .nav-tab {
            padding: 12px 0;
            color: #5f6368;
            text-decoration: none;
            font-size: 14px;
            border-bottom: 3px solid transparent;
            transition: all 0.2s;
        }
        
        .nav-tab.active {
            color: #1a73e8;
            border-bottom-color: #1a73e8;
        }
        
        .nav-tab:hover {
            color: #1a73e8;
        }
        
        .main-content {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .results-info {
            color: #70757a;
            font-size: 14px;
            margin-bottom: 20px;
        }
        
        .result-item {
            margin-bottom: 30px;
            max-width: 600px;
        }
        
        .result-url {
            color: #202124;
            font-size: 14px;
            margin-bottom: 3px;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .result-title {
            color: #1a0dab;
            font-size: 20px;
            text-decoration: none;
            display: block;
            margin-bottom: 3px;
            line-height: 1.3;
        }
        
        .result-title:hover {
            text-decoration: underline;
        }
        
        .result-title:visited {
            color: #609;
        }
        
        .result-snippet {
            color: #4d5156;
            font-size: 14px;
            line-height: 1.58;
        }
        
        .loading {
            text-align: center;
            padding: 40px;
            color: #70757a;
        }
        
        .spinner {
            border: 2px solid #f3f3f3;
            border-top: 2px solid #4285f4;
            border-radius: 50%;
            width: 30px;
            height: 30px;
            animation: spin 1s linear infinite;
            margin: 0 auto 20px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .pagination {
            margin-top: 40px;
            display: flex;
            justify-content: center;
            gap: 10px;
        }
        
        .page-btn {
            padding: 8px 12px;
            border: none;
            background: none;
            color: #1a73e8;
            cursor: pointer;
            border-radius: 4px;
            font-size: 14px;
        }
        
        .page-btn:hover {
            background: #f1f3f4;
        }
        
        .page-btn.active {
            background: #1a73e8;
            color: white;
        }
        
        .page-btn:disabled {
            color: #dadce0;
            cursor: not-allowed;
        }
        
        @media (max-width: 768px) {
            .header-content {
                flex-direction: column;
                gap: 15px;
                align-items: stretch;
            }
            
            .nav-content {
                overflow-x: auto;
                white-space: nowrap;
            }
            
            .main-content {
                padding: 15px;
            }
            
            .result-title {
                font-size: 18px;
            }
        }
    </style>
    
    <link rel="stylesheet" href="styles/main.css">
</head>
<body>
    <header class="header">
        <div class="header-content">
            <a href="index.html" class="logo">Search</a>
            <div class="search-container">
                <input 
                    type="text" 
                    class="search-box" 
                    id="search-input"
                    placeholder="Search..."
                    autocomplete="off"
                >
                <svg class="search-icon" id="search-btn" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <circle cx="11" cy="11" r="8"></circle>
                    <path d="m21 21-4.35-4.35"></path>
                </svg>
            </div>
        </div>
    </header>
    
    <nav class="nav-tabs">
        <div class="nav-content">
            <a href="#" class="nav-tab active" data-type="web">All</a>
            <a href="#" class="nav-tab" data-type="images">Images</a>
            <a href="#" class="nav-tab" data-type="videos">Videos</a>
            <a href="#" class="nav-tab" data-type="news">News</a>
        </div>
    </nav>
    
    <main class="main-content">
        <div class="results-info" id="results-info"></div>
        
        <div class="loading" id="loading">
            <div class="spinner"></div>
            <div>Searching...</div>
        </div>
        
        <div class="results-container" id="results-container"></div>
        
        <div class="pagination" id="pagination"></div>
    </main>
    
    <script src="js/utils.js"></script>
    <script src="js/results.js"></script>
    <script src="js/voice-search.js"></script>
    <script src="js/advanced-search.js"></script>
    <!-- <script src="js/instant-answers.js"></script> -->
    <script src="js/search-tools.js"></script>
</body>
</html>
