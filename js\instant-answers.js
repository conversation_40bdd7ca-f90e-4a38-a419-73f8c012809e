// Instant Answers functionality - Calculator, Weather, Definitions, etc.

class InstantAnswers {
    constructor() {
        this.patterns = {
            calculator: /^[\d\+\-\*\/\(\)\.\s]+$/,
            weather: /^weather\s+(?:in\s+)?(.+)$/i,
            definition: /^(?:define|definition|what\s+is)\s+(.+)$/i,
            time: /^(?:time|what\s+time)\s+(?:in\s+)?(.+)$/i,
            currency: /^(\d+(?:\.\d+)?)\s*([a-z]{3})\s+(?:to|in)\s+([a-z]{3})$/i,
            unit: /^(\d+(?:\.\d+)?)\s*([a-z]+)\s+(?:to|in)\s+([a-z]+)$/i,
            color: /^#?([a-f0-9]{6}|[a-f0-9]{3})$/i,
            ip: /^(?:my\s+ip|ip\s+address)$/i,
            translate: /^translate\s+(.+)\s+(?:to|into)\s+([a-z]+)$/i
        };
        this.init();
    }
    
    init() {
        // Hook into search functionality
        this.interceptSearchQueries();
    }
    
    interceptSearchQueries() {
        // Override the search function to check for instant answers first
        const originalPerformSearch = window.SearchEngine?.prototype?.performSearch;
        if (originalPerformSearch) {
            window.SearchEngine.prototype.performSearch = async function(query, start = 1, searchType = 'web') {
                // Check for instant answers first
                const instantAnswer = await window.instantAnswers.checkForInstantAnswer(query);
                if (instantAnswer) {
                    window.instantAnswers.displayInstantAnswer(instantAnswer, query);
                    return;
                }
                
                // Fall back to original search
                return originalPerformSearch.call(this, query, start, searchType);
            };
        }
    }
    
    async checkForInstantAnswer(query) {
        const trimmedQuery = query.trim();
        
        // Calculator
        if (this.patterns.calculator.test(trimmedQuery)) {
            return await this.calculateExpression(trimmedQuery);
        }
        
        // Weather
        const weatherMatch = trimmedQuery.match(this.patterns.weather);
        if (weatherMatch) {
            return await this.getWeather(weatherMatch[1]);
        }
        
        // Definition
        const definitionMatch = trimmedQuery.match(this.patterns.definition);
        if (definitionMatch) {
            return await this.getDefinition(definitionMatch[1]);
        }
        
        // Time
        const timeMatch = trimmedQuery.match(this.patterns.time);
        if (timeMatch) {
            return await this.getTime(timeMatch[1]);
        }
        
        // Currency conversion
        const currencyMatch = trimmedQuery.match(this.patterns.currency);
        if (currencyMatch) {
            return await this.convertCurrency(currencyMatch[1], currencyMatch[2], currencyMatch[3]);
        }
        
        // Unit conversion
        const unitMatch = trimmedQuery.match(this.patterns.unit);
        if (unitMatch) {
            return await this.convertUnits(unitMatch[1], unitMatch[2], unitMatch[3]);
        }
        
        // Color code
        if (this.patterns.color.test(trimmedQuery)) {
            return this.getColorInfo(trimmedQuery);
        }
        
        // IP address
        if (this.patterns.ip.test(trimmedQuery)) {
            return await this.getIPAddress();
        }
        
        return null;
    }
    
    async calculateExpression(expression) {
        try {
            // Sanitize expression for security
            const sanitized = expression.replace(/[^0-9+\-*/().\s]/g, '');
            if (!sanitized) return null;
            
            // Use Function constructor for safe evaluation
            const result = Function(`"use strict"; return (${sanitized})`)();
            
            if (isNaN(result) || !isFinite(result)) {
                return null;
            }
            
            return {
                type: 'calculator',
                title: 'Calculator',
                result: result.toString(),
                expression: expression,
                html: `
                    <div class="instant-answer calculator">
                        <div class="answer-header">
                            <span class="answer-icon">🧮</span>
                            <span class="answer-title">Calculator</span>
                        </div>
                        <div class="answer-content">
                            <div class="calculation">
                                <div class="expression">${Utils.escapeHtml(expression)} =</div>
                                <div class="result">${result}</div>
                            </div>
                        </div>
                    </div>
                `
            };
        } catch (error) {
            return null;
        }
    }
    
    async getWeather(location) {
        // Mock weather data (in real implementation, use weather API)
        const mockWeather = {
            location: location,
            temperature: Math.floor(Math.random() * 30) + 10,
            condition: ['Sunny', 'Cloudy', 'Rainy', 'Partly Cloudy'][Math.floor(Math.random() * 4)],
            humidity: Math.floor(Math.random() * 40) + 40,
            windSpeed: Math.floor(Math.random() * 20) + 5
        };
        
        return {
            type: 'weather',
            title: `Weather in ${location}`,
            result: `${mockWeather.temperature}°C, ${mockWeather.condition}`,
            html: `
                <div class="instant-answer weather">
                    <div class="answer-header">
                        <span class="answer-icon">🌤️</span>
                        <span class="answer-title">Weather in ${Utils.escapeHtml(location)}</span>
                    </div>
                    <div class="answer-content">
                        <div class="weather-info">
                            <div class="temperature">${mockWeather.temperature}°C</div>
                            <div class="condition">${mockWeather.condition}</div>
                            <div class="details">
                                <span>Humidity: ${mockWeather.humidity}%</span>
                                <span>Wind: ${mockWeather.windSpeed} km/h</span>
                            </div>
                        </div>
                    </div>
                </div>
            `
        };
    }
    
    async getDefinition(word) {
        // Mock definition (in real implementation, use dictionary API)
        const mockDefinitions = {
            'javascript': 'A high-level, interpreted programming language that conforms to the ECMAScript specification.',
            'algorithm': 'A process or set of rules to be followed in calculations or other problem-solving operations.',
            'computer': 'An electronic device for storing and processing data, typically in binary form.',
            'internet': 'A global computer network providing a variety of information and communication facilities.'
        };
        
        const definition = mockDefinitions[word.toLowerCase()] || 
                          `A word or concept that may have multiple meanings. Search for more detailed information.`;
        
        return {
            type: 'definition',
            title: `Definition of ${word}`,
            result: definition,
            html: `
                <div class="instant-answer definition">
                    <div class="answer-header">
                        <span class="answer-icon">📖</span>
                        <span class="answer-title">Definition</span>
                    </div>
                    <div class="answer-content">
                        <div class="word">${Utils.escapeHtml(word)}</div>
                        <div class="definition-text">${Utils.escapeHtml(definition)}</div>
                    </div>
                </div>
            `
        };
    }
    
    async getTime(location) {
        const now = new Date();
        const timeString = now.toLocaleTimeString();
        const dateString = now.toLocaleDateString();
        
        return {
            type: 'time',
            title: `Time in ${location}`,
            result: timeString,
            html: `
                <div class="instant-answer time">
                    <div class="answer-header">
                        <span class="answer-icon">🕐</span>
                        <span class="answer-title">Time in ${Utils.escapeHtml(location)}</span>
                    </div>
                    <div class="answer-content">
                        <div class="current-time">${timeString}</div>
                        <div class="current-date">${dateString}</div>
                    </div>
                </div>
            `
        };
    }
    
    async convertCurrency(amount, fromCurrency, toCurrency) {
        // Mock exchange rates (in real implementation, use currency API)
        const exchangeRates = {
            'usd': { 'eur': 0.85, 'gbp': 0.73, 'jpy': 110, 'cad': 1.25 },
            'eur': { 'usd': 1.18, 'gbp': 0.86, 'jpy': 129, 'cad': 1.47 },
            'gbp': { 'usd': 1.37, 'eur': 1.16, 'jpy': 150, 'cad': 1.71 }
        };
        
        const rate = exchangeRates[fromCurrency.toLowerCase()]?.[toCurrency.toLowerCase()] || 1;
        const result = (parseFloat(amount) * rate).toFixed(2);
        
        return {
            type: 'currency',
            title: 'Currency Conversion',
            result: `${amount} ${fromCurrency.toUpperCase()} = ${result} ${toCurrency.toUpperCase()}`,
            html: `
                <div class="instant-answer currency">
                    <div class="answer-header">
                        <span class="answer-icon">💱</span>
                        <span class="answer-title">Currency Conversion</span>
                    </div>
                    <div class="answer-content">
                        <div class="conversion">
                            <span class="from">${amount} ${fromCurrency.toUpperCase()}</span>
                            <span class="equals">=</span>
                            <span class="to">${result} ${toCurrency.toUpperCase()}</span>
                        </div>
                        <div class="rate">Exchange rate: 1 ${fromCurrency.toUpperCase()} = ${rate} ${toCurrency.toUpperCase()}</div>
                    </div>
                </div>
            `
        };
    }
    
    async convertUnits(value, fromUnit, toUnit) {
        // Mock unit conversions
        const conversions = {
            // Length
            'm': { 'ft': 3.28084, 'in': 39.3701, 'cm': 100, 'km': 0.001 },
            'ft': { 'm': 0.3048, 'in': 12, 'cm': 30.48, 'km': 0.0003048 },
            'in': { 'm': 0.0254, 'ft': 0.0833, 'cm': 2.54, 'km': 0.0000254 },
            // Weight
            'kg': { 'lb': 2.20462, 'g': 1000, 'oz': 35.274 },
            'lb': { 'kg': 0.453592, 'g': 453.592, 'oz': 16 },
            // Temperature
            'c': { 'f': (c) => c * 9/5 + 32, 'k': (c) => c + 273.15 },
            'f': { 'c': (f) => (f - 32) * 5/9, 'k': (f) => (f - 32) * 5/9 + 273.15 }
        };
        
        const conversion = conversions[fromUnit.toLowerCase()]?.[toUnit.toLowerCase()];
        if (!conversion) return null;
        
        const result = typeof conversion === 'function' ? 
                      conversion(parseFloat(value)) : 
                      (parseFloat(value) * conversion);
        
        return {
            type: 'unit',
            title: 'Unit Conversion',
            result: `${value} ${fromUnit} = ${result.toFixed(2)} ${toUnit}`,
            html: `
                <div class="instant-answer unit">
                    <div class="answer-header">
                        <span class="answer-icon">📏</span>
                        <span class="answer-title">Unit Conversion</span>
                    </div>
                    <div class="answer-content">
                        <div class="conversion">
                            <span class="from">${value} ${fromUnit}</span>
                            <span class="equals">=</span>
                            <span class="to">${result.toFixed(2)} ${toUnit}</span>
                        </div>
                    </div>
                </div>
            `
        };
    }
    
    getColorInfo(colorCode) {
        const hex = colorCode.replace('#', '');
        const r = parseInt(hex.substr(0, 2), 16);
        const g = parseInt(hex.substr(2, 2), 16);
        const b = parseInt(hex.substr(4, 2), 16);
        
        return {
            type: 'color',
            title: 'Color Information',
            result: `#${hex.toUpperCase()}`,
            html: `
                <div class="instant-answer color">
                    <div class="answer-header">
                        <span class="answer-icon">🎨</span>
                        <span class="answer-title">Color Information</span>
                    </div>
                    <div class="answer-content">
                        <div class="color-preview" style="background-color: #${hex}; width: 100px; height: 100px; border-radius: 8px; margin: 10px 0;"></div>
                        <div class="color-values">
                            <div>HEX: #${hex.toUpperCase()}</div>
                            <div>RGB: rgb(${r}, ${g}, ${b})</div>
                            <div>HSL: ${this.rgbToHsl(r, g, b)}</div>
                        </div>
                    </div>
                </div>
            `
        };
    }
    
    async getIPAddress() {
        // Mock IP address (in real implementation, use IP API)
        const mockIP = '192.168.1.' + Math.floor(Math.random() * 255);
        
        return {
            type: 'ip',
            title: 'Your IP Address',
            result: mockIP,
            html: `
                <div class="instant-answer ip">
                    <div class="answer-header">
                        <span class="answer-icon">🌐</span>
                        <span class="answer-title">Your IP Address</span>
                    </div>
                    <div class="answer-content">
                        <div class="ip-address">${mockIP}</div>
                        <div class="ip-info">
                            <div>Location: Demo City, Demo Country</div>
                            <div>ISP: Demo Internet Provider</div>
                        </div>
                    </div>
                </div>
            `
        };
    }
    
    displayInstantAnswer(answer, query) {
        // Navigate to results page with instant answer
        const params = new URLSearchParams({
            q: query,
            instant: 'true'
        });
        
        // Store instant answer in session storage
        sessionStorage.setItem('instantAnswer', JSON.stringify(answer));
        
        window.location.href = `results.html?${params.toString()}`;
    }
    
    // Helper function for color conversion
    rgbToHsl(r, g, b) {
        r /= 255;
        g /= 255;
        b /= 255;
        
        const max = Math.max(r, g, b);
        const min = Math.min(r, g, b);
        let h, s, l = (max + min) / 2;
        
        if (max === min) {
            h = s = 0;
        } else {
            const d = max - min;
            s = l > 0.5 ? d / (2 - max - min) : d / (max + min);
            
            switch (max) {
                case r: h = (g - b) / d + (g < b ? 6 : 0); break;
                case g: h = (b - r) / d + 2; break;
                case b: h = (r - g) / d + 4; break;
            }
            h /= 6;
        }
        
        return `hsl(${Math.round(h * 360)}, ${Math.round(s * 100)}%, ${Math.round(l * 100)}%)`;
    }
    
    // Add instant answer styles
    addInstantAnswerStyles() {
        const style = document.createElement('style');
        style.textContent = `
            .instant-answer {
                background: #f8f9fa;
                border: 1px solid #dadce0;
                border-radius: 8px;
                padding: 20px;
                margin-bottom: 20px;
                box-shadow: 0 1px 3px rgba(0,0,0,0.1);
            }
            
            .answer-header {
                display: flex;
                align-items: center;
                margin-bottom: 15px;
            }
            
            .answer-icon {
                font-size: 20px;
                margin-right: 10px;
            }
            
            .answer-title {
                font-size: 16px;
                font-weight: 500;
                color: #202124;
            }
            
            .answer-content {
                font-size: 14px;
                color: #3c4043;
            }
            
            .calculator .expression {
                font-size: 16px;
                color: #5f6368;
                margin-bottom: 5px;
            }
            
            .calculator .result {
                font-size: 24px;
                font-weight: 500;
                color: #202124;
            }
            
            .weather-info .temperature {
                font-size: 32px;
                font-weight: 300;
                color: #202124;
                margin-bottom: 5px;
            }
            
            .weather-info .condition {
                font-size: 16px;
                color: #5f6368;
                margin-bottom: 10px;
            }
            
            .weather-info .details {
                display: flex;
                gap: 20px;
                font-size: 14px;
                color: #5f6368;
            }
            
            .definition .word {
                font-size: 20px;
                font-weight: 500;
                color: #202124;
                margin-bottom: 10px;
            }
            
            .definition .definition-text {
                line-height: 1.5;
            }
            
            .time .current-time {
                font-size: 28px;
                font-weight: 300;
                color: #202124;
                margin-bottom: 5px;
            }
            
            .time .current-date {
                font-size: 16px;
                color: #5f6368;
            }
            
            .currency .conversion,
            .unit .conversion {
                font-size: 20px;
                margin-bottom: 10px;
            }
            
            .currency .from,
            .unit .from {
                color: #5f6368;
            }
            
            .currency .equals,
            .unit .equals {
                margin: 0 10px;
                color: #202124;
            }
            
            .currency .to,
            .unit .to {
                font-weight: 500;
                color: #202124;
            }
            
            .currency .rate {
                font-size: 14px;
                color: #5f6368;
            }
            
            .color .color-values div {
                margin: 5px 0;
                font-family: monospace;
            }
            
            .ip .ip-address {
                font-size: 24px;
                font-weight: 500;
                color: #202124;
                margin-bottom: 10px;
                font-family: monospace;
            }
            
            .ip .ip-info div {
                margin: 5px 0;
                color: #5f6368;
            }
        `;
        document.head.appendChild(style);
    }
}

// Initialize instant answers when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.instantAnswers = new InstantAnswers();
    window.instantAnswers.addInstantAnswerStyles();
});

// Export for use in other modules
window.InstantAnswers = InstantAnswers;
