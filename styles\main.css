/* Main stylesheet for Google Clone - Performance optimized */

/* CSS Reset and Base Styles */
*,
*::before,
*::after {
    box-sizing: border-box;
}

html {
    font-size: 16px;
    -webkit-text-size-adjust: 100%;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

body {
    margin: 0;
    padding: 0;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
    line-height: 1.4;
    color: #202124;
    background-color: #fff;
    min-height: 100vh;
}

/* Focus styles for accessibility */
*:focus {
    outline: 2px solid #4285f4;
    outline-offset: 2px;
}

button:focus,
input:focus {
    outline-offset: 0;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
    margin: 0;
    font-weight: 400;
}

a {
    color: #1a0dab;
    text-decoration: none;
}

a:hover {
    text-decoration: underline;
}

a:visited {
    color: #609;
}

/* Button styles */
button {
    font-family: inherit;
    font-size: inherit;
    border: none;
    background: none;
    cursor: pointer;
    padding: 0;
}

.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 10px 20px;
    border: 1px solid #f8f9fa;
    border-radius: 4px;
    background-color: #f8f9fa;
    color: #3c4043;
    font-size: 14px;
    font-weight: 400;
    text-decoration: none;
    transition: all 0.1s ease;
    user-select: none;
    white-space: nowrap;
}

.btn:hover {
    background-color: #f1f3f4;
    border-color: #dadce0;
    box-shadow: 0 1px 1px rgba(0,0,0,.1);
    color: #202124;
}

.btn:active {
    background-color: #e8eaed;
}

.btn:focus {
    outline: 2px solid #4285f4;
    outline-offset: 2px;
}

.btn:disabled {
    background-color: #f8f9fa;
    border-color: #f8f9fa;
    color: #80868b;
    cursor: not-allowed;
}

/* Input styles */
input[type="text"],
input[type="search"] {
    font-family: inherit;
    font-size: 16px;
    border: 1px solid #dfe1e5;
    border-radius: 24px;
    padding: 0 16px;
    height: 44px;
    width: 100%;
    outline: none;
    transition: box-shadow 0.2s ease;
    background-color: #fff;
    color: #202124;
}

input[type="text"]:hover,
input[type="search"]:hover {
    box-shadow: 0 2px 5px 1px rgba(64,60,67,.16);
}

input[type="text"]:focus,
input[type="search"]:focus {
    box-shadow: 0 2px 5px 1px rgba(64,60,67,.16);
    border-color: transparent;
}

/* Loading animation */
.spinner {
    border: 2px solid #f3f3f3;
    border-top: 2px solid #4285f4;
    border-radius: 50%;
    width: 30px;
    height: 30px;
    animation: spin 1s linear infinite;
    margin: 0 auto;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Utility classes */
.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

.text-center {
    text-align: center;
}

.hidden {
    display: none !important;
}

.loading {
    opacity: 0.6;
    pointer-events: none;
}

/* Error styles */
.error-message {
    background-color: #fce8e6;
    border: 1px solid #f28b82;
    border-radius: 8px;
    color: #d93025;
    padding: 16px;
    margin: 16px 0;
    font-size: 14px;
}

.error-message strong {
    display: block;
    margin-bottom: 8px;
}

/* Success styles */
.success-message {
    background-color: #e6f4ea;
    border: 1px solid #34a853;
    border-radius: 8px;
    color: #137333;
    padding: 16px;
    margin: 16px 0;
    font-size: 14px;
}

/* Warning styles */
.warning-message {
    background-color: #fef7e0;
    border: 1px solid #fbbc04;
    border-radius: 8px;
    color: #b06000;
    padding: 16px;
    margin: 16px 0;
    font-size: 14px;
}

/* Responsive images */
img {
    max-width: 100%;
    height: auto;
    display: block;
}

img.lazy {
    opacity: 0;
    transition: opacity 0.3s;
}

img.lazy.loaded {
    opacity: 1;
}

/* Skip link for accessibility */
.skip-link {
    position: absolute;
    top: -40px;
    left: 6px;
    background: #000;
    color: #fff;
    padding: 8px;
    text-decoration: none;
    border-radius: 4px;
    z-index: 1000;
}

.skip-link:focus {
    top: 6px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .btn {
        border: 2px solid #000;
    }
    
    input[type="text"],
    input[type="search"] {
        border: 2px solid #000;
    }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* Print styles */
@media print {
    .header,
    .nav-tabs,
    .pagination,
    .btn {
        display: none !important;
    }
    
    body {
        font-size: 12pt;
        line-height: 1.5;
    }
    
    a {
        color: #000 !important;
        text-decoration: underline !important;
    }
    
    .result-item {
        page-break-inside: avoid;
        margin-bottom: 20pt;
    }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
    body {
        background-color: #202124;
        color: #e8eaed;
    }
    
    .btn {
        background-color: #303134;
        border-color: #5f6368;
        color: #e8eaed;
    }
    
    .btn:hover {
        background-color: #3c4043;
        border-color: #5f6368;
    }
    
    input[type="text"],
    input[type="search"] {
        background-color: #303134;
        border-color: #5f6368;
        color: #e8eaed;
    }
    
    .result-title {
        color: #8ab4f8;
    }
    
    .result-title:visited {
        color: #c58af9;
    }
    
    .result-snippet {
        color: #bdc1c6;
    }
    
    .result-url {
        color: #9aa0a6;
    }
    
    .suggestions {
        background-color: #303134;
        border-color: #5f6368;
    }
    
    .suggestion-item:hover,
    .suggestion-item.selected {
        background-color: #3c4043;
    }
}
