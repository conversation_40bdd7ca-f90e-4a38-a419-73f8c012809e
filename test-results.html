<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Search Results</title>
    <style>
        body {
            font-family: arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: #fff;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
        }
        
        .header {
            padding: 20px 0;
            border-bottom: 1px solid #ddd;
            margin-bottom: 20px;
        }
        
        .search-box {
            width: 100%;
            max-width: 500px;
            padding: 12px;
            border: 1px solid #ddd;
            border-radius: 24px;
            font-size: 16px;
            outline: none;
        }
        
        .search-box:focus {
            border-color: #4285f4;
            box-shadow: 0 2px 5px rgba(66, 133, 244, 0.3);
        }
        
        .search-btn {
            background: #4285f4;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 4px;
            cursor: pointer;
            margin-left: 10px;
            font-size: 14px;
        }
        
        .search-btn:hover {
            background: #3367d6;
        }
        
        .results-info {
            color: #70757a;
            font-size: 14px;
            margin: 20px 0;
        }
        
        .result-item {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #e8eaed;
            border-radius: 8px;
            background: #f8f9fa;
        }
        
        .result-title {
            color: #1a0dab;
            font-size: 20px;
            margin-bottom: 5px;
            text-decoration: none;
            display: block;
        }
        
        .result-title:hover {
            text-decoration: underline;
        }
        
        .result-url {
            color: #006621;
            font-size: 14px;
            margin-bottom: 8px;
        }
        
        .result-snippet {
            color: #4d5156;
            font-size: 14px;
            line-height: 1.58;
        }
        
        .loading {
            text-align: center;
            padding: 40px;
            color: #70757a;
        }
        
        .error {
            background: #fce8e6;
            border: 1px solid #f28b82;
            color: #d93025;
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
        }
        
        .success {
            background: #e6f4ea;
            border: 1px solid #34a853;
            color: #137333;
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
        }
        
        .debug-info {
            background: #e8f0fe;
            border: 1px solid #4285f4;
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
            font-family: monospace;
            font-size: 12px;
        }
        
        .test-buttons {
            margin: 20px 0;
        }
        
        .test-btn {
            background: #34a853;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
            font-size: 12px;
        }
        
        .test-btn:hover {
            background: #2d7d32;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔍 Test Search Results</h1>
            <p>This page will definitely show search results!</p>
            
            <div>
                <input type="text" class="search-box" id="search-input" placeholder="Enter search query" value="">
                <button class="search-btn" onclick="performSearch()">Search</button>
            </div>
            
            <div class="test-buttons">
                <button class="test-btn" onclick="showTestResults()">Show Test Results</button>
                <button class="test-btn" onclick="testAPI()">Test Real API</button>
                <button class="test-btn" onclick="clearResults()">Clear</button>
                <button class="test-btn" onclick="showDebugInfo()">Debug Info</button>
            </div>
        </div>
        
        <div id="status"></div>
        <div id="results-info"></div>
        <div id="results-container"></div>
        <div id="debug-info"></div>
    </div>
    
    <script>
        // Configuration
        const API_KEY = 'AIzaSyDo2zq98fZbNEgjkdsYGAZs-CJcfSBz9OQ';
        const SEARCH_ENGINE_ID = '61201925358ea4e83';
        const API_URL = 'https://www.googleapis.com/customsearch/v1';
        
        // Log everything for debugging
        console.log('🚀 Test Results Page Loaded');
        console.log('API Key:', API_KEY ? API_KEY.substring(0, 10) + '...' : 'Not set');
        console.log('Search Engine ID:', SEARCH_ENGINE_ID ? SEARCH_ENGINE_ID.substring(0, 10) + '...' : 'Not set');
        
        // Initialize page
        document.addEventListener('DOMContentLoaded', function() {
            console.log('📄 DOM Content Loaded');
            
            // Check URL parameters
            const urlParams = new URLSearchParams(window.location.search);
            const query = urlParams.get('q');
            
            if (query) {
                console.log('🔍 Found query in URL:', query);
                document.getElementById('search-input').value = query;
                showStatus('Found query: ' + query, 'success');
                performSearch();
            } else {
                console.log('📝 No query in URL, showing test results');
                showTestResults();
            }
            
            // Add enter key listener
            document.getElementById('search-input').addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    performSearch();
                }
            });
        });
        
        function showStatus(message, type = 'info') {
            const statusDiv = document.getElementById('status');
            const className = type === 'error' ? 'error' : type === 'success' ? 'success' : 'debug-info';
            statusDiv.innerHTML = `<div class="${className}">${message}</div>`;
            console.log('📊 Status:', message);
        }
        
        function showTestResults() {
            console.log('🧪 Showing test results');
            showStatus('Showing test results', 'success');
            
            const testResults = {
                searchInformation: {
                    totalResults: "1,234,567",
                    searchTime: 0.45
                },
                items: [
                    {
                        title: "Test Result 1 - JavaScript Tutorial",
                        link: "https://developer.mozilla.org/en-US/docs/Web/JavaScript",
                        snippet: "JavaScript (JS) is a lightweight, interpreted programming language with first-class functions. Learn the basics of JavaScript programming.",
                        displayLink: "developer.mozilla.org"
                    },
                    {
                        title: "Test Result 2 - JavaScript Guide",
                        link: "https://javascript.info/",
                        snippet: "The Modern JavaScript Tutorial: simple, but detailed explanations with examples and tasks, including closures, document and events.",
                        displayLink: "javascript.info"
                    },
                    {
                        title: "Test Result 3 - JavaScript Reference",
                        link: "https://www.w3schools.com/js/",
                        snippet: "Well organized and easy to understand Web building tutorials with lots of examples of how to use HTML, CSS, JavaScript, SQL, Python, PHP, Bootstrap, Java, XML and more.",
                        displayLink: "w3schools.com"
                    },
                    {
                        title: "Test Result 4 - JavaScript Examples",
                        link: "https://www.freecodecamp.org/learn/javascript-algorithms-and-data-structures/",
                        snippet: "Learn JavaScript fundamentals like variables, arrays, objects, loops, and functions. Then learn about DOM manipulation, algorithmic thinking, and object-oriented programming.",
                        displayLink: "freecodecamp.org"
                    },
                    {
                        title: "Test Result 5 - JavaScript Frameworks",
                        link: "https://reactjs.org/",
                        snippet: "A JavaScript library for building user interfaces. React makes it painless to create interactive UIs. Design simple views for each state in your application.",
                        displayLink: "reactjs.org"
                    }
                ]
            };
            
            displayResults(testResults);
        }
        
        async function performSearch() {
            const query = document.getElementById('search-input').value.trim();
            
            if (!query) {
                showStatus('Please enter a search query', 'error');
                return;
            }
            
            console.log('🔍 Performing search for:', query);
            showStatus('Searching for: ' + query, 'info');
            
            // Show loading
            document.getElementById('results-container').innerHTML = '<div class="loading">🔄 Searching...</div>';
            
            try {
                // Try real API first
                const results = await searchWithAPI(query);
                displayResults(results);
                showStatus('✅ Search completed successfully!', 'success');
            } catch (error) {
                console.error('❌ Search failed:', error);
                showStatus('API search failed, showing demo results: ' + error.message, 'error');
                
                // Fallback to demo results
                const demoResults = createDemoResults(query);
                displayResults(demoResults);
            }
        }
        
        async function searchWithAPI(query) {
            console.log('🌐 Making API request for:', query);
            
            const params = new URLSearchParams({
                key: API_KEY,
                cx: SEARCH_ENGINE_ID,
                q: query,
                num: 10
            });
            
            const url = `${API_URL}?${params.toString()}`;
            console.log('📡 API URL:', url);
            
            const response = await fetch(url);
            console.log('📥 API Response status:', response.status);
            
            if (!response.ok) {
                const errorData = await response.json();
                console.error('❌ API Error:', errorData);
                throw new Error(`API Error: ${response.status} - ${errorData.error?.message || 'Unknown error'}`);
            }
            
            const data = await response.json();
            console.log('✅ API Response data:', data);
            
            return data;
        }
        
        function createDemoResults(query) {
            console.log('🎭 Creating demo results for:', query);
            
            return {
                searchInformation: {
                    totalResults: "987,654",
                    searchTime: 0.23
                },
                items: [
                    {
                        title: `${query} - Demo Result 1`,
                        link: `https://example.com/${query.toLowerCase().replace(/\s+/g, '-')}-1`,
                        snippet: `This is a demo search result for "${query}". This shows that the search interface is working correctly. In a real implementation, this would be replaced with actual search results from Google's Custom Search API.`,
                        displayLink: "example.com"
                    },
                    {
                        title: `${query} - Demo Result 2`,
                        link: `https://demo.com/${query.toLowerCase().replace(/\s+/g, '-')}-2`,
                        snippet: `Another demo result for your search query "${query}". The search functionality is working properly and displaying results in the correct format.`,
                        displayLink: "demo.com"
                    },
                    {
                        title: `${query} - Demo Result 3`,
                        link: `https://test.com/${query.toLowerCase().replace(/\s+/g, '-')}-3`,
                        snippet: `Third demo result showing that the search results are being displayed correctly. Query: "${query}". This demonstrates the search interface functionality.`,
                        displayLink: "test.com"
                    }
                ]
            };
        }
        
        function displayResults(data) {
            console.log('📋 Displaying results:', data);
            
            const resultsInfo = document.getElementById('results-info');
            const resultsContainer = document.getElementById('results-container');
            
            if (!data || !data.items || data.items.length === 0) {
                resultsInfo.innerHTML = '<div class="error">No results found</div>';
                resultsContainer.innerHTML = '<div class="error">No search results to display</div>';
                return;
            }
            
            // Update results info
            const totalResults = data.searchInformation?.totalResults || '0';
            const searchTime = data.searchInformation?.searchTime || 0;
            resultsInfo.innerHTML = `<div class="results-info">About ${totalResults} results (${searchTime} seconds)</div>`;
            
            // Display results
            let resultsHTML = '';
            
            data.items.forEach((item, index) => {
                resultsHTML += `
                    <div class="result-item">
                        <a href="${escapeHtml(item.link)}" class="result-title" target="_blank" rel="noopener">
                            ${escapeHtml(item.title)}
                        </a>
                        <div class="result-url">${escapeHtml(item.displayLink || item.link)}</div>
                        <div class="result-snippet">${escapeHtml(item.snippet || 'No description available')}</div>
                    </div>
                `;
            });
            
            resultsContainer.innerHTML = resultsHTML;
            console.log('✅ Results displayed successfully');
        }
        
        async function testAPI() {
            console.log('🧪 Testing API connection');
            showStatus('Testing API connection...', 'info');
            
            try {
                const testUrl = `${API_URL}?key=${API_KEY}&cx=${SEARCH_ENGINE_ID}&q=test&num=1`;
                console.log('🔗 Test URL:', testUrl);
                
                const response = await fetch(testUrl);
                console.log('📡 Test response status:', response.status);
                
                if (response.ok) {
                    const data = await response.json();
                    console.log('✅ API test successful:', data);
                    showStatus('✅ API connection successful! Found ' + (data.searchInformation?.totalResults || 'unknown') + ' results', 'success');
                } else {
                    const errorData = await response.json();
                    console.error('❌ API test failed:', errorData);
                    showStatus('❌ API test failed: ' + (errorData.error?.message || 'Unknown error'), 'error');
                }
            } catch (error) {
                console.error('❌ API test error:', error);
                showStatus('❌ API test error: ' + error.message, 'error');
            }
        }
        
        function clearResults() {
            console.log('🧹 Clearing results');
            document.getElementById('results-info').innerHTML = '';
            document.getElementById('results-container').innerHTML = '';
            document.getElementById('status').innerHTML = '';
            document.getElementById('debug-info').innerHTML = '';
            showStatus('Results cleared', 'info');
        }
        
        function showDebugInfo() {
            console.log('🐛 Showing debug info');
            
            const debugInfo = {
                'Page URL': window.location.href,
                'User Agent': navigator.userAgent,
                'API Key': API_KEY ? API_KEY.substring(0, 10) + '...' : 'Not set',
                'Search Engine ID': SEARCH_ENGINE_ID ? SEARCH_ENGINE_ID.substring(0, 10) + '...' : 'Not set',
                'Current Time': new Date().toISOString(),
                'Local Storage Available': typeof(Storage) !== "undefined",
                'Fetch API Available': typeof(fetch) !== "undefined"
            };
            
            let debugHTML = '<div class="debug-info"><h3>🐛 Debug Information</h3>';
            for (const [key, value] of Object.entries(debugInfo)) {
                debugHTML += `<div><strong>${key}:</strong> ${value}</div>`;
            }
            debugHTML += '</div>';
            
            document.getElementById('debug-info').innerHTML = debugHTML;
        }
        
        function escapeHtml(text) {
            const div = document.createElement('div');
            div.textContent = text;
            return div.innerHTML;
        }
        
        // Global error handler
        window.addEventListener('error', function(e) {
            console.error('💥 Global error:', e.error);
            showStatus('JavaScript error: ' + e.message, 'error');
        });
        
        console.log('✅ All functions loaded successfully');
    </script>
</body>
</html>
