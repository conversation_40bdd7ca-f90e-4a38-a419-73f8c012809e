<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Search</title>
    <meta name="description" content="Fast and simple search engine">
    
    <!-- Preload critical resources -->
    <link rel="preload" href="styles/main.css" as="style">
    <link rel="preload" href="js/search.js" as="script">
    
    <!-- Critical CSS inlined for performance -->
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #fff;
            color: #202124;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
        }
        
        .header {
            padding: 15px 20px;
            display: flex;
            justify-content: flex-end;
            align-items: center;
            gap: 15px;
        }
        
        .header a {
            color: #202124;
            text-decoration: none;
            font-size: 13px;
        }
        
        .header a:hover {
            text-decoration: underline;
        }
        
        .main-container {
            flex: 1;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            padding: 0 20px;
            margin-top: -150px;
        }
        
        .logo {
            font-size: 90px;
            font-weight: 400;
            color: #4285f4;
            margin-bottom: 30px;
            letter-spacing: -5px;
        }
        
        .search-container {
            width: 100%;
            max-width: 584px;
            position: relative;
        }
        
        .search-box {
            width: 100%;
            height: 44px;
            border: 1px solid #dfe1e5;
            border-radius: 24px;
            padding: 0 45px 0 16px;
            font-size: 16px;
            outline: none;
            transition: box-shadow 0.2s;
        }
        
        .search-box:hover {
            box-shadow: 0 2px 5px 1px rgba(64,60,67,.16);
        }
        
        .search-box:focus {
            box-shadow: 0 2px 5px 1px rgba(64,60,67,.16);
            border-color: transparent;
        }
        
        .search-icon {
            position: absolute;
            right: 12px;
            top: 50%;
            transform: translateY(-50%);
            width: 20px;
            height: 20px;
            cursor: pointer;
        }
        
        .buttons {
            margin-top: 30px;
            display: flex;
            gap: 15px;
            justify-content: center;
        }
        
        .btn {
            background: #f8f9fa;
            border: 1px solid #f8f9fa;
            border-radius: 4px;
            color: #3c4043;
            font-size: 14px;
            padding: 10px 20px;
            cursor: pointer;
            transition: all 0.1s;
        }
        
        .btn:hover {
            box-shadow: 0 1px 1px rgba(0,0,0,.1);
            background: #f1f3f4;
            border: 1px solid #dadce0;
        }
        
        .suggestions {
            position: absolute;
            top: 100%;
            left: 0;
            right: 0;
            background: white;
            border: 1px solid #dfe1e5;
            border-top: none;
            border-radius: 0 0 24px 24px;
            max-height: 300px;
            overflow-y: auto;
            z-index: 1000;
            display: none;
        }
        
        .suggestion-item {
            padding: 8px 16px;
            cursor: pointer;
            font-size: 16px;
            border-bottom: 1px solid #f1f3f4;
        }
        
        .suggestion-item:hover,
        .suggestion-item.selected {
            background: #f1f3f4;
        }
        
        .suggestion-item:last-child {
            border-bottom: none;
        }
        
        @media (max-width: 768px) {
            .logo {
                font-size: 60px;
                margin-bottom: 20px;
            }
            
            .main-container {
                margin-top: -100px;
            }
            
            .buttons {
                flex-direction: column;
                align-items: center;
            }
            
            .btn {
                width: 200px;
            }
        }
    </style>
    
    <link rel="stylesheet" href="styles/main.css">
    <link rel="manifest" href="manifest.json">
</head>
<body>
    <header class="header">
        <a href="#" id="images-link">Images</a>
        <a href="#" id="videos-link">Videos</a>
        <button class="btn" id="settings-btn">Settings</button>
    </header>
    
    <main class="main-container">
        <div class="logo">Search</div>
        
        <div class="search-container">
            <input 
                type="text" 
                class="search-box" 
                id="search-input"
                placeholder="Search the web..."
                autocomplete="off"
                spellcheck="false"
                role="combobox"
                aria-expanded="false"
                aria-autocomplete="list"
            >
            <svg class="search-icon" id="search-btn" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <circle cx="11" cy="11" r="8"></circle>
                <path d="m21 21-4.35-4.35"></path>
            </svg>
            
            <div class="suggestions" id="suggestions" role="listbox"></div>
        </div>
        
        <div class="buttons">
            <button class="btn" id="search-button">Search</button>
            <button class="btn" id="lucky-button">I'm Feeling Lucky</button>
        </div>
    </main>
    
    <!-- Scripts loaded at the end for performance -->
    <script src="js/utils.js"></script>
    <script src="js/autocomplete.js"></script>
    <script src="js/search.js"></script>
    <script src="js/voice-search.js"></script>
    <script src="js/advanced-search.js"></script>
    <!-- <script src="js/instant-answers.js"></script> -->
    
    <!-- Service Worker Registration -->
    <script>
        if ('serviceWorker' in navigator) {
            window.addEventListener('load', () => {
                navigator.serviceWorker.register('/service-worker.js')
                    .then(registration => console.log('SW registered'))
                    .catch(error => console.log('SW registration failed'));
            });
        }
    </script>
</body>
</html>
