// Results page functionality for displaying Google Custom Search results

class ResultsPage {
    constructor() {
        this.currentQuery = '';
        this.currentPage = 1;
        this.currentType = 'web';
        this.totalResults = 0;
        this.isLoading = false;
        this.init();
    }
    
    init() {
        this.bindEvents();
        this.loadFromUrl();
        
        // Check if API is configured
        if (!Utils.checkApiConfiguration()) {
            this.showApiConfigurationWarning();
            return;
        }
        
        // Perform initial search if query exists
        if (this.currentQuery) {
            this.performSearch();
        }
    }
    
    bindEvents() {
        const searchInput = document.getElementById('search-input');
        const searchIcon = document.getElementById('search-btn');
        
        // Search input events
        if (searchInput) {
            searchInput.addEventListener('keydown', (e) => {
                if (e.key === 'Enter') {
                    e.preventDefault();
                    const query = searchInput.value.trim();
                    if (query) {
                        this.updateSearch(query, 1, this.currentType);
                    }
                }
            });
        }
        
        if (searchIcon) {
            searchIcon.addEventListener('click', () => {
                const query = searchInput?.value.trim() || '';
                if (query) {
                    this.updateSearch(query, 1, this.currentType);
                }
            });
        }
        
        // Navigation tabs
        document.querySelectorAll('.nav-tab').forEach(tab => {
            tab.addEventListener('click', (e) => {
                e.preventDefault();
                const type = tab.getAttribute('data-type');
                this.updateSearch(this.currentQuery, 1, type);
            });
        });
        
        // Handle browser back/forward
        window.addEventListener('popstate', () => {
            this.loadFromUrl();
            this.performSearch();
        });
    }
    
    loadFromUrl() {
        const params = Utils.getUrlParams();
        this.currentQuery = params.q;
        this.currentPage = Math.max(1, Math.floor((params.start - 1) / Utils.CONFIG.RESULTS_PER_PAGE) + 1);
        this.currentType = params.type;
        
        // Update UI
        const searchInput = document.getElementById('search-input');
        if (searchInput) {
            searchInput.value = this.currentQuery;
        }
        
        // Update page title
        if (this.currentQuery) {
            document.title = `${this.currentQuery} - Search Results`;
        }
        
        // Update active tab
        this.updateActiveTab();
    }
    
    updateActiveTab() {
        document.querySelectorAll('.nav-tab').forEach(tab => {
            tab.classList.remove('active');
            if (tab.getAttribute('data-type') === this.currentType) {
                tab.classList.add('active');
            }
        });
    }
    
    updateSearch(query, page = 1, type = 'web') {
        this.currentQuery = query;
        this.currentPage = page;
        this.currentType = type;
        
        const start = (page - 1) * Utils.CONFIG.RESULTS_PER_PAGE + 1;
        
        // Update URL
        Utils.updateUrlParams({
            q: query,
            start: start,
            type: type
        });
        
        // Update page title
        document.title = `${query} - Search Results`;
        
        // Update active tab
        this.updateActiveTab();
        
        // Perform search
        this.performSearch();
    }
    
    async performSearch() {
        if (!this.currentQuery || this.isLoading) return;
        
        this.isLoading = true;
        this.showLoading();
        
        try {
            const start = (this.currentPage - 1) * Utils.CONFIG.RESULTS_PER_PAGE + 1;
            const results = await this.searchGoogle(this.currentQuery, start, this.currentType);
            
            this.hideLoading();
            this.renderResults(results);
            this.renderPagination(results);
            
        } catch (error) {
            console.error('Search failed:', error);
            this.hideLoading();
            this.showError(error);
        } finally {
            this.isLoading = false;
        }
    }
    
    async searchGoogle(query, start = 1, searchType = 'web') {
        const cacheKey = `${query}-${start}-${searchType}`;

        // Check cache first
        const cached = Utils.searchCache.get(cacheKey);
        if (cached) {
            return cached;
        }

        // Check if API is configured, if not return demo data
        if (!Utils.checkApiConfiguration()) {
            return this.getDemoResults(query, start, searchType);
        }

        try {
            // Build API URL
            const params = new URLSearchParams({
                key: Utils.CONFIG.GOOGLE_API_KEY,
                cx: Utils.CONFIG.SEARCH_ENGINE_ID,
                q: query,
                start: start,
                num: Utils.CONFIG.RESULTS_PER_PAGE
            });

            // Add search type specific parameters
            if (searchType === 'images') {
                params.append('searchType', 'image');
            } else if (searchType === 'videos') {
                params.set('q', `${query} site:youtube.com OR site:vimeo.com`);
            } else if (searchType === 'news') {
                params.append('tbm', 'nws');
            }

            const url = `${Utils.CONFIG.BASE_URL}?${params.toString()}`;
            console.log('Making API request to:', url);

            const response = await fetch(url);

            if (!response.ok) {
                console.error('API request failed:', response.status, response.statusText);
                throw new Error(`Search failed: ${response.status} ${response.statusText}`);
            }

            const data = await response.json();
            console.log('API response:', data);

            // Cache the results
            Utils.searchCache.set(cacheKey, data);

            return data;
        } catch (error) {
            console.error('Search API error, falling back to demo data:', error);
            return this.getDemoResults(query, start, searchType);
        }
    }

    getDemoResults(query, start = 1, searchType = 'web') {
        // Return demo data for testing
        const demoData = {
            searchInformation: {
                totalResults: "1234567",
                searchTime: 0.45
            },
            items: [
                {
                    title: `Demo Result 1 for "${query}"`,
                    link: "https://example.com/1",
                    snippet: "This is a demo search result. The actual Google Custom Search API is not configured yet. Please follow the setup instructions in the README to get real search results.",
                    displayLink: "example.com"
                },
                {
                    title: `Demo Result 2 for "${query}"`,
                    link: "https://example.com/2",
                    snippet: "Another demo result showing how the search interface works. Configure your Google API key and Search Engine ID to see real results.",
                    displayLink: "example.com"
                },
                {
                    title: `Demo Result 3 for "${query}"`,
                    link: "https://example.com/3",
                    snippet: "This demonstrates the search results layout and functionality. The interface is fully working and ready for real API integration.",
                    displayLink: "example.com"
                },
                {
                    title: `Demo Result 4 for "${query}"`,
                    link: "https://example.com/4",
                    snippet: "Fast, responsive search results with proper pagination and multiple search types. Built with pure JavaScript for maximum performance.",
                    displayLink: "example.com"
                },
                {
                    title: `Demo Result 5 for "${query}"`,
                    link: "https://example.com/5",
                    snippet: "High-performance Google clone with autocomplete, caching, and offline support. No heavy frameworks - just optimized vanilla JavaScript.",
                    displayLink: "example.com"
                }
            ]
        };

        // Add more results for pagination testing
        if (start > 1) {
            demoData.items = demoData.items.map((item, index) => ({
                ...item,
                title: `${item.title} - Page ${Math.ceil(start / 10)}`,
                snippet: `${item.snippet} (Result ${start + index})`
            }));
        }

        return demoData;
    }
    
    renderResults(data) {
        const container = document.getElementById('results-container');
        const infoElement = document.getElementById('results-info');
        
        if (!container) return;
        
        // Update results info
        if (data.searchInformation && infoElement) {
            const totalResults = parseInt(data.searchInformation.totalResults);
            const searchTime = parseFloat(data.searchInformation.searchTime);
            infoElement.textContent = `About ${Utils.formatNumber(totalResults)} results (${searchTime} seconds)`;
            this.totalResults = totalResults;
        }
        
        // Clear previous results
        container.innerHTML = '';
        
        if (!data.items || data.items.length === 0) {
            container.innerHTML = `
                <div style="text-align: center; padding: 40px; color: #70757a;">
                    <p>No results found for "${Utils.escapeHtml(this.currentQuery)}"</p>
                    <p style="margin-top: 10px; font-size: 14px;">Try different keywords or check your spelling.</p>
                </div>
            `;
            return;
        }
        
        // Render results based on type
        if (this.currentType === 'images') {
            this.renderImageResults(data.items, container);
        } else {
            this.renderWebResults(data.items, container);
        }
    }
    
    renderWebResults(items, container) {
        items.forEach(item => {
            const resultDiv = document.createElement('div');
            resultDiv.className = 'result-item';
            
            const domain = Utils.extractDomain(item.link);
            const title = Utils.escapeHtml(item.title);
            const snippet = Utils.escapeHtml(item.snippet || '');
            
            resultDiv.innerHTML = `
                <div class="result-url">
                    <span>${domain}</span>
                </div>
                <a href="${item.link}" class="result-title" target="_blank" rel="noopener">
                    ${title}
                </a>
                <div class="result-snippet">${snippet}</div>
            `;
            
            container.appendChild(resultDiv);
        });
    }
    
    renderImageResults(items, container) {
        const gridDiv = document.createElement('div');
        gridDiv.style.cssText = `
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 20px;
        `;
        
        items.forEach(item => {
            const imageDiv = document.createElement('div');
            imageDiv.style.cssText = `
                border-radius: 8px;
                overflow: hidden;
                background: #f8f9fa;
                cursor: pointer;
            `;
            
            imageDiv.innerHTML = `
                <img 
                    src="${item.link}" 
                    alt="${Utils.escapeHtml(item.title)}"
                    style="width: 100%; height: 150px; object-fit: cover;"
                    loading="lazy"
                    onerror="this.style.display='none'"
                >
                <div style="padding: 10px; font-size: 12px; color: #5f6368;">
                    ${Utils.escapeHtml(item.title)}
                </div>
            `;
            
            imageDiv.addEventListener('click', () => {
                window.open(item.image?.contextLink || item.link, '_blank');
            });
            
            gridDiv.appendChild(imageDiv);
        });
        
        container.appendChild(gridDiv);
    }
    
    renderPagination(data) {
        const paginationContainer = document.getElementById('pagination');
        if (!paginationContainer) return;
        
        paginationContainer.innerHTML = '';
        
        const totalPages = Math.min(10, Math.ceil(this.totalResults / Utils.CONFIG.RESULTS_PER_PAGE));
        
        if (totalPages <= 1) return;
        
        // Previous button
        if (this.currentPage > 1) {
            const prevBtn = this.createPageButton('Previous', this.currentPage - 1);
            paginationContainer.appendChild(prevBtn);
        }
        
        // Page numbers
        const startPage = Math.max(1, this.currentPage - 2);
        const endPage = Math.min(totalPages, startPage + 4);
        
        for (let i = startPage; i <= endPage; i++) {
            const pageBtn = this.createPageButton(i.toString(), i, i === this.currentPage);
            paginationContainer.appendChild(pageBtn);
        }
        
        // Next button
        if (this.currentPage < totalPages) {
            const nextBtn = this.createPageButton('Next', this.currentPage + 1);
            paginationContainer.appendChild(nextBtn);
        }
    }
    
    createPageButton(text, page, isActive = false) {
        const button = document.createElement('button');
        button.className = `page-btn ${isActive ? 'active' : ''}`;
        button.textContent = text;
        button.disabled = isActive;
        
        if (!isActive) {
            button.addEventListener('click', () => {
                this.updateSearch(this.currentQuery, page, this.currentType);
            });
        }
        
        return button;
    }
    
    showLoading() {
        const loading = document.getElementById('loading');
        const container = document.getElementById('results-container');
        const pagination = document.getElementById('pagination');
        
        if (loading) loading.style.display = 'block';
        if (container) container.innerHTML = '';
        if (pagination) pagination.innerHTML = '';
    }
    
    hideLoading() {
        const loading = document.getElementById('loading');
        if (loading) loading.style.display = 'none';
    }
    
    showError(error) {
        const container = document.getElementById('results-container');
        if (!container) return;
        
        const errorDiv = Utils.handleError(error, 'search');
        container.appendChild(errorDiv);
    }
    
    showApiConfigurationWarning() {
        const container = document.getElementById('results-container');
        if (!container) return;
        
        container.innerHTML = `
            <div style="background: #fff3cd; border: 1px solid #ffeaa7; padding: 20px; border-radius: 8px; color: #856404; margin: 20px 0;">
                <strong>⚠️ API Configuration Required</strong><br><br>
                To display real search results, please configure your Google Custom Search API:
                <ol style="margin: 15px 0; padding-left: 20px;">
                    <li>Get a Google API key from <a href="https://console.developers.google.com/" target="_blank">Google Cloud Console</a></li>
                    <li>Create a Custom Search Engine at <a href="https://cse.google.com/" target="_blank">Google CSE</a></li>
                    <li>Update the GOOGLE_API_KEY and SEARCH_ENGINE_ID in js/utils.js</li>
                </ol>
                <small>The search interface is fully functional and ready to use once configured.</small>
            </div>
        `;
    }
}

// Initialize results page when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.resultsPage = new ResultsPage();
});

// Export for use in other modules
window.ResultsPage = ResultsPage;
